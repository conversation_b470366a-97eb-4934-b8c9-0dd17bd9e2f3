
def factorial(n):
    if n == 0 or n == 1:
        return 1
    result = 1
    for i in range(2, n + 1):
        result = result * i
    return result
def is_strong_number(num):
    original_num = num
    total = 0

    # Get each digit and add its factorial
    while num > 0:
        digit = num % 10
        total = total + factorial(digit)
        num = num // 10

    return total == original_num

# Show how strong number works
def show_strong_number(num):
    print(f"Checking {num}:")
    original_num = num
    total = 0
    digits = []

    # Get digits
    while num > 0:
        digit = num % 10
        digits.append(digit)
        num = num // 10

    digits.reverse()  # Put digits in correct order

    # Calculate factorial sum
    for digit in digits:
        fact = factorial(digit)
        total = total + fact
        print(f"  {digit}! = {fact}")

    print(f"  Total = {total}")

    if total == original_num:
        print(f"  {original_num} is a STRONG NUMBER! ✓")
    else:
        print(f"  {original_num} is NOT a strong number ✗")
    print()

# Main program
print("=== Simple Strong Number Finder ===")
print()

# Show some factorials
print("Factorials:")
for i in range(6):
    print(f"{i}! = {factorial(i)}")
print()

# Test some numbers
test_numbers = [1, 2, 145, 123]
print("Testing numbers:")
for num in test_numbers:
    show_strong_number(num)

# Find all strong numbers up to 1000
print("Finding strong numbers up to 1000:")
strong_numbers = []
for num in range(1, 1001):
    if is_strong_number(num):
        strong_numbers.append(num)

print(f"Strong numbers found: {strong_numbers}")
print()
print("Note: Only 1, 2, 145, and 40585 are strong numbers!")