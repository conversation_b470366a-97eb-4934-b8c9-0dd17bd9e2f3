import math

def factorial_iterative(n):
    """Calculate factorial using iterative approach"""
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n == 0 or n == 1:
        return 1

    result = 1
    for i in range(2, n + 1):
        result *= i
    return result

def factorial_recursive(n):
    """Calculate factorial using recursive approach"""
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n == 0 or n == 1:
        return 1
    return n * factorial_recursive(n - 1)

def factorial_builtin(n):
    """Calculate factorial using Python's built-in math.factorial"""
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    return math.factorial(n)

# Use the iterative version as the main factorial function for efficiency
def factorial(n):
    """Main factorial function - uses iterative approach for efficiency"""
    return factorial_iterative(n)

def compare_factorial_methods(n):
    """Compare different factorial calculation methods"""
    print(f"Calculating factorial of {n}:")
    print(f"  Iterative: {factorial_iterative(n)}")
    print(f"  Recursive: {factorial_recursive(n)}")
    print(f"  Built-in:  {factorial_builtin(n)}")
    print()

def is_strong_number(num):
    if num < 0:
        return False

    original_num = num
    digit_factorial_sum = 0
    while num > 0:
        digit = num % 10
        digit_factorial_sum += factorial(digit)
        num //= 10

    return digit_factorial_sum == original_num

def find_strong_numbers_in_range(start, end):
    strong_numbers = []
    for num in range(start, end + 1):
        if is_strong_number(num):
            strong_numbers.append(num)
    return strong_numbers

def find_strong_numbers_up_to(limit):
    return find_strong_numbers_in_range(1, limit)

def demonstrate_strong_number(num):
    if num < 0:
        print(f"{num} is negative, so it cannot be a strong number.")
        return

    original_num = num
    digits = []
    factorials = []
    total_sum = 0
    temp_num = num
    while temp_num > 0:
        digit = temp_num % 10
        digits.insert(0, digit) 
        fact = factorial(digit)
        factorials.insert(0, fact)
        total_sum += fact
        temp_num //= 10
    if num == 0:
        digits = [0]
        factorials = [1]
        total_sum = 1

    print(f"Number: {original_num}")
    print(f"Digits: {digits}")
    print(f"Factorials: {factorials}")
    print(f"Sum of factorials: {' + '.join(map(str, factorials))} = {total_sum}")

    if total_sum == original_num:
        print(f"✓ {original_num} is a STRONG NUMBER!")
    else:
        print(f"✗ {original_num} is NOT a strong number.")
    print()

if __name__ == "__main__":
    print("=== Factorial & Strong Number Program ===\n")

    # Demonstrate factorial calculations
    print("1. Factorial Function Demonstration:")
    print("=" * 40)
    factorial_test_numbers = [0, 1, 2, 3, 4, 5, 8, 9]
    for num in factorial_test_numbers:
        print(f"{num}! = {factorial(num)}")
    print()

    # Compare factorial methods for a specific number
    print("2. Comparing Factorial Calculation Methods:")
    print("=" * 45)
    compare_factorial_methods(5)

    # Strong number demonstration
    print("3. Strong Number Analysis:")
    print("=" * 30)
    test_numbers = [1, 2, 145, 40585, 123, 999]

    print("Testing individual numbers:")
    for num in test_numbers:
        demonstrate_strong_number(num)

    print("Finding all strong numbers up to 100,000:")
    strong_nums = find_strong_numbers_up_to(100000)
    print(f"Strong numbers found: {strong_nums}")
    print(f"Total count: {len(strong_nums)}")

    print("\nNote: The only strong numbers are 1, 2, 145, and 40585!")

    # Show how factorials are used in strong number calculation
    print("\n4. How Factorials Are Used in Strong Numbers:")
    print("=" * 50)
    print("For 145: 1! + 4! + 5! = 1 + 24 + 120 = 145")
    print(f"  1! = {factorial(1)}")
    print(f"  4! = {factorial(4)}")
    print(f"  5! = {factorial(5)}")
    print(f"  Sum = {factorial(1) + factorial(4) + factorial(5)}")