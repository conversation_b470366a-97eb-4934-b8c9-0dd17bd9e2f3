import math

def factorial(n):
    if n == 0 or n == 1:
        return 1
    return math.factorial(n)

def is_strong_number(num):
    if num < 0:
        return False

    original_num = num
    digit_factorial_sum = 0
    while num > 0:
        digit = num % 10
        digit_factorial_sum += factorial(digit)
        num //= 10

    return digit_factorial_sum == original_num

def find_strong_numbers_in_range(start, end):
    strong_numbers = []
    for num in range(start, end + 1):
        if is_strong_number(num):
            strong_numbers.append(num)
    return strong_numbers

def find_strong_numbers_up_to(limit):
    return find_strong_numbers_in_range(1, limit)

def demonstrate_strong_number(num):
    if num < 0:
        print(f"{num} is negative, so it cannot be a strong number.")
        return

    original_num = num
    digits = []
    factorials = []
    total_sum = 0
    temp_num = num
    while temp_num > 0:
        digit = temp_num % 10
        digits.insert(0, digit) 
        fact = factorial(digit)
        factorials.insert(0, fact)
        total_sum += fact
        temp_num //= 10
    if num == 0:
        digits = [0]
        factorials = [1]
        total_sum = 1

    print(f"Number: {original_num}")
    print(f"Digits: {digits}")
    print(f"Factorials: {factorials}")
    print(f"Sum of factorials: {' + '.join(map(str, factorials))} = {total_sum}")

    if total_sum == original_num:
        print(f"✓ {original_num} is a STRONG NUMBER!")
    else:
        print(f"✗ {original_num} is NOT a strong number.")
    print()

# Main execution
if __name__ == "__main__":
    print("=== Strong Number Finder ===\n")

    # Test some known strong numbers
    test_numbers = [1, 2, 145, 40585, 123, 999]

    print("Testing individual numbers:")
    for num in test_numbers:
        demonstrate_strong_number(num)

    # Find all strong numbers up to 100000
    print("Finding all strong numbers up to 100,000:")
    strong_nums = find_strong_numbers_up_to(100000)
    print(f"Strong numbers found: {strong_nums}")
    print(f"Total count: {len(strong_nums)}")

    print("\nNote: The only strong numbers are 1, 2, 145, and 40585!")